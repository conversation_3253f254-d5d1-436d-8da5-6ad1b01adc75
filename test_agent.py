#!/usr/bin/env python3
"""
Test script for Dealer AI Agent
Tests the complete workflow with various user prompts
"""

import asyncio
import json
import os
import sys
from dotenv import load_dotenv

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.agent import process_whatsapp_message, get_agent

load_dotenv()

# Test messages in different languages and scenarios
TEST_MESSAGES = [
    # Swahili product requests
    {
        "message": "Nionyeshe simu za Samsung chini ya laki tano",
        "description": "Swahili: Samsung phones under 500,000",
        "expected_type": "product_request"
    },
    {
        "message": "Nataka laptop ya Dell iliyotumika",
        "description": "Swahili: Used Dell laptop",
        "expected_type": "product_request"
    },
    
    # English product requests
    {
        "message": "Show me Samsung phones under 400,000 TZS",
        "description": "English: Samsung phones with price limit",
        "expected_type": "product_request"
    },
    {
        "message": "I need a used laptop",
        "description": "English: General laptop request",
        "expected_type": "product_request"
    },
    
    # General conversation - Swahili
    {
        "message": "<PERSON>ja<PERSON>, habari za asubuhi?",
        "description": "Swahili: Morning greeting",
        "expected_type": "conversational"
    },
    {
        "message": "Nifafanulie Dealer AI inafanya nini",
        "description": "Swahili: What does Dealer AI do?",
        "expected_type": "conversational"
    },
    
    # General conversation - English
    {
        "message": "Hello, how are you?",
        "description": "English: Greeting",
        "expected_type": "conversational"
    },
    {
        "message": "What can you help me with?",
        "description": "English: Capabilities inquiry",
        "expected_type": "conversational"
    },
    
    # Edge cases
    {
        "message": "simu",
        "description": "Single word: phone",
        "expected_type": "product_request"
    },
    {
        "message": "xyz123 random text",
        "description": "Random text",
        "expected_type": "conversational"
    }
]

async def test_agent_initialization():
    """Test that the agent initializes correctly"""
    print("🔧 Testing agent initialization...")
    
    try:
        agent = get_agent()
        print(f"✅ Agent initialized successfully with {len(agent.tools)} tools")
        
        # Test tool names
        tool_names = [tool.name for tool in agent.tools]
        expected_tools = [
            "intent_extractor",
            "web_scraper", 
            "product_processor",
            "conversational_responder",
            "whatsapp_sender"
        ]
        
        for expected_tool in expected_tools:
            if expected_tool in tool_names:
                print(f"  ✅ Tool '{expected_tool}' found")
            else:
                print(f"  ❌ Tool '{expected_tool}' missing")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent initialization failed: {e}")
        return False

async def test_individual_tools():
    """Test individual tools"""
    print("\n🔧 Testing individual tools...")
    
    try:
        agent = get_agent()
        
        # Test intent extraction
        print("  Testing intent extraction...")
        intent_tool = next((tool for tool in agent.tools if tool.name == "intent_extractor"), None)
        if intent_tool:
            result = await intent_tool._arun("Nionyeshe simu za Samsung")
            intent_data = json.loads(result)
            if intent_data.get("is_product_request"):
                print("    ✅ Intent extraction working")
            else:
                print("    ❌ Intent extraction failed")
        
        # Test conversational responder
        print("  Testing conversational responder...")
        conv_tool = next((tool for tool in agent.tools if tool.name == "conversational_responder"), None)
        if conv_tool:
            intent_json = json.dumps({"language": "swahili", "is_product_request": False})
            result = await conv_tool._arun("Hujambo", intent_json)
            response_data = json.loads(result)
            if response_data.get("success"):
                print("    ✅ Conversational responder working")
            else:
                print("    ❌ Conversational responder failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Tool testing failed: {e}")
        return False

async def test_message_processing():
    """Test message processing with various inputs"""
    print("\n🔧 Testing message processing...")
    
    results = []
    
    for i, test_case in enumerate(TEST_MESSAGES, 1):
        print(f"\n  Test {i}/{len(TEST_MESSAGES)}: {test_case['description']}")
        print(f"  Message: '{test_case['message']}'")
        
        try:
            result = await process_whatsapp_message(
                test_case['message'], 
                "+255000000000"
            )
            
            if result.get("success"):
                print(f"    ✅ Processed successfully")
                print(f"    Response length: {len(result.get('response', ''))}")
            else:
                print(f"    ❌ Processing failed: {result.get('error', 'Unknown error')}")
            
            results.append({
                "test_case": test_case,
                "result": result,
                "success": result.get("success", False)
            })
            
        except Exception as e:
            print(f"    ❌ Exception occurred: {e}")
            results.append({
                "test_case": test_case,
                "result": {"error": str(e)},
                "success": False
            })
    
    return results

async def test_jiji_scraping():
    """Test jiji.co.tz scraping specifically"""
    print("\n🔧 Testing Jiji.co.tz scraping...")
    
    try:
        # Test with a simple product request
        test_message = "Samsung phone"
        print(f"  Testing with: '{test_message}'")
        
        result = await process_whatsapp_message(test_message, "+255000000000")
        
        if result.get("success"):
            print("    ✅ Jiji scraping test completed")
            # Check if products were found (this might fail if no products match)
            response = result.get("response", "")
            if "deal" in response.lower() or "product" in response.lower():
                print("    ✅ Products appear to be found")
            else:
                print("    ⚠️  No products found (this might be normal)")
        else:
            print(f"    ❌ Jiji scraping failed: {result.get('error', 'Unknown error')}")
        
        return result.get("success", False)
        
    except Exception as e:
        print(f"    ❌ Jiji scraping test failed: {e}")
        return False

def print_summary(results):
    """Print test summary"""
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r["success"])
    
    print(f"Total tests: {total_tests}")
    print(f"Successful: {successful_tests}")
    print(f"Failed: {total_tests - successful_tests}")
    print(f"Success rate: {(successful_tests/total_tests)*100:.1f}%")
    
    print("\n📋 Detailed Results:")
    for i, result in enumerate(results, 1):
        status = "✅" if result["success"] else "❌"
        test_desc = result["test_case"]["description"]
        print(f"  {i:2d}. {status} {test_desc}")
        
        if not result["success"]:
            error = result["result"].get("error", "Unknown error")
            print(f"      Error: {error}")

async def main():
    """Main test function"""
    print("🚀 Starting Dealer AI Agent Tests")
    print("="*60)
    
    # Check environment
    required_env_vars = ["OPENROUTER_API_KEY", "TWILIO_SID", "TWILIO_AUTH_TOKEN"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        print("Please check your .env file")
        return
    
    # Run tests
    init_success = await test_agent_initialization()
    if not init_success:
        print("❌ Agent initialization failed. Stopping tests.")
        return
    
    tools_success = await test_individual_tools()
    if not tools_success:
        print("⚠️  Some tools failed, but continuing with message tests...")
    
    message_results = await test_message_processing()
    
    jiji_success = await test_jiji_scraping()
    
    # Print summary
    print_summary(message_results)
    
    print(f"\n🌐 Jiji scraping test: {'✅ Passed' if jiji_success else '❌ Failed'}")
    
    print("\n🎉 Testing completed!")
    print("\nNote: Some tests may fail due to network issues, site changes, or missing dependencies.")
    print("This is normal for a complex web scraping system.")

if __name__ == "__main__":
    asyncio.run(main())
