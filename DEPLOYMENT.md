# 🚀 Dealer AI Deployment Guide

## Quick Start Commands

```bash
# 1. Install everything
python install.py

# 2. Configure environment
cp .env.example .env
# Edit .env with your credentials

# 3. Test the system
python demo.py

# 4. Start the server
python start.py server
```

## 🌐 Production Deployment

### Option 1: Local Development
```bash
# Start with auto-reload
python start.py server

# Or manually
uvicorn core.endpoint:app --reload --host 0.0.0.0 --port 8000
```

### Option 2: Cloud Deployment (Recommended)

#### Using Railway
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
```

#### Using Heroku
```bash
# Create Procfile
echo "web: uvicorn core.endpoint:app --host 0.0.0.0 --port \$PORT" > Procfile

# Deploy
heroku create your-dealer-ai
heroku config:set OPENROUTER_API_KEY=your_key
heroku config:set TWILIO_SID=your_sid
heroku config:set TWILIO_AUTH_TOKEN=your_token
git push heroku main
```

#### Using DigitalOcean App Platform
```yaml
# app.yaml
name: dealer-ai
services:
- name: web
  source_dir: /
  github:
    repo: your-username/dealer-ai
    branch: main
  run_command: uvicorn core.endpoint:app --host 0.0.0.0 --port 8080
  environment_slug: python
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: OPENROUTER_API_KEY
    value: your_key
  - key: TWILIO_SID
    value: your_sid
  - key: TWILIO_AUTH_TOKEN
    value: your_token
```

## 🔧 Environment Configuration

### Required Variables
```env
# Twilio WhatsApp API
TWILIO_SID=**********************************
TWILIO_AUTH_TOKEN=c90c1ab2f98e5f33ddfaeff8d98fde69
TWILIO_WHATSAPP_FROM=whatsapp:+14155238886
TWILIO_WHATSAPP_TO=whatsapp:+255625290997

# OpenRouter API for GPT-3.5
OPENROUTER_API_KEY=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

# Optional Configuration
DEBUG=false
MAX_PRODUCTS=20
SCROLL_ATTEMPTS=3
RETRY_ATTEMPTS=3
```

### Twilio Webhook Setup
1. Go to Twilio Console → WhatsApp → Sandbox
2. Set webhook URL to: `https://your-domain.com/whatsapp`
3. Set HTTP method to: `POST`

## 📊 Monitoring & Health Checks

### Health Check Endpoint
```bash
curl https://your-domain.com/health
```

Expected response:
```json
{
  "status": "healthy",
  "agent": "initialized", 
  "tools": 5,
  "message": "All systems operational"
}
```

### Test Endpoint
```bash
curl -X POST https://your-domain.com/test \
  -H "Content-Type: application/json" \
  -d '{"message": "Samsung phone", "phone": "+255000000000"}'
```

## 🔒 Security Considerations

### Environment Variables
- Never commit `.env` files to version control
- Use secure environment variable management in production
- Rotate API keys regularly

### Rate Limiting
Consider adding rate limiting for production:
```python
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

@app.post("/whatsapp")
@limiter.limit("10/minute")
async def whatsapp(request: Request):
    # ... existing code
```

## 📈 Scaling Considerations

### Horizontal Scaling
- The agent is stateless and can be scaled horizontally
- Use a load balancer to distribute requests
- Consider Redis for shared state if needed

### Performance Optimization
```python
# In production, consider:
# 1. Connection pooling for HTTP requests
# 2. Browser instance reuse for Playwright
# 3. Caching for frequently requested products
# 4. Async processing for heavy operations
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Playwright Browser Not Found
```bash
playwright install chromium
```

#### 2. Import Errors
```bash
pip install -r requirements.txt
```

#### 3. Environment Variables Not Loaded
```bash
# Check .env file exists and has correct format
cat .env
```

#### 4. Twilio Webhook Not Working
- Verify webhook URL is publicly accessible
- Check Twilio webhook logs in console
- Ensure HTTPS is used (required by Twilio)

### Debug Mode
```bash
# Enable debug mode
export DEBUG=true
python start.py server
```

### Logs
```bash
# Check application logs
tail -f logs/dealer_ai.log

# Check system status
python start.py status
```

## 🔄 Updates & Maintenance

### Updating Dependencies
```bash
pip install -r requirements.txt --upgrade
playwright install chromium
```

### Adding New Sites
1. Add site profile to `core/site_profiles.py`
2. Test with new site
3. Update documentation

### Monitoring
- Set up health check monitoring
- Monitor API usage (OpenRouter, Twilio)
- Track response times and error rates

## 🎯 Production Checklist

- [ ] Environment variables configured
- [ ] Twilio webhook set up
- [ ] Health checks working
- [ ] Error handling tested
- [ ] Rate limiting implemented
- [ ] Monitoring set up
- [ ] Backup strategy in place
- [ ] Documentation updated

## 🆘 Support

If you encounter issues:
1. Check the health endpoint
2. Run the test suite: `python test_agent.py`
3. Check logs for error messages
4. Verify all environment variables are set
5. Test individual components with `python start.py message "test"`

Your Dealer AI is now ready for production! 🚀
