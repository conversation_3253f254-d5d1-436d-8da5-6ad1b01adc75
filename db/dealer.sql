CREATE TABLE "users" (
  "id" integer PRIMARY KEY,
  "phone_no" string UNIQUE,
  "whatsapp_name" string,
  "preferences" json,
  "created_at" timestamp
);

CREATE TABLE "platforms" (
  "id" integer PRIMARY KEY,
  "name" string,
  "affiliate_id" string,
  "api" string,
  "joined_at" timestamp
);

CREATE TABLE "vendors" (
  "id" integer PRIMARY KEY,
  "name" varchar,
  "store_url" string,
  "platform" int,
  "created_at" timestamp
);

CREATE TABLE "categories" (
  "id" integer PRIMARY KEY,
  "name" string,
  "parent" int
);

CREATE TABLE "products" (
  "id" integer PRIMARY KEY,
  "name" string,
  "description" string,
  "price" decimal,
  "img_url" string,
  "product_url" string,
  "category" int,
  "vendor" int,
  "uploaded_at" timestamp
);

CREATE TABLE "prompt_queries" (
  "id" integer PRIMARY KEY,
  "user" int,
  "prompt" stringPromptQuery,
  "parsed_values" json,
  "create_at" timestamp
);

CREATE TABLE "search_results" (
  "id" integer PRIMARY KEY,
  "prompt" int,
  "results" json_array,
  "sent_at" timestamp
);

ALTER TABLE "prompt_queries" ADD FOREIGN KEY ("user") REFERENCES "users" ("id");

ALTER TABLE "vendors" ADD FOREIGN KEY ("platform") REFERENCES "platforms" ("id");

ALTER TABLE "products" ADD FOREIGN KEY ("vendor") REFERENCES "vendors" ("id");

ALTER TABLE "products" ADD FOREIGN KEY ("category") REFERENCES "categories" ("id");

ALTER TABLE "categories" ADD FOREIGN KEY ("id") REFERENCES "categories" ("parent");

ALTER TABLE "search_results" ADD FOREIGN KEY ("prompt") REFERENCES "prompt_queries" ("id");
