# Dealer AI - Autonomous WhatsApp Shopping Assistant

Dealer AI is an intelligent, WhatsApp-based shopping assistant that helps users find the best product deals across multiple e-commerce platforms — simply by chatting in natural language.

Using **GPT-3.5-turbo** and **Lang<PERSON>hain + Playwright**, Dealer AI acts as an autonomous agent that:

- 🧠 **Understands** user requests in Swahili, English, or any other language
- 🌐 **Visits** websites like Jiji.co.tz automatically
- 🔍 **Searches** for products and applies price or condition filters intelligently
- 📊 **Extracts** and formats top deals from search results
- 💬 **Returns** clean, conversational responses via WhatsApp

Unlike traditional bots that follow fixed rules, Dealer AI uses **real-time scraping**, **GPT-powered interpretation**, and **dynamic reasoning** to intelligently perform searches and deliver deals — even if websites change or the user speaks differently.

## 🚀 Key Features

- **Autonomous Agent**: Fully autonomous GPT-3.5-turbo agent using LangChain
- **Multi-language Support**: Understands Swahili, English, and other languages
- **Dynamic Web Scraping**: Uses Playwright with GPT-assisted element detection
- **Intelligent Filtering**: Automatically applies price and condition filters
- **WhatsApp Integration**: Seamless communication via Twilio WhatsApp API
- **Retry Logic**: Robust error handling and retry mechanisms
- **Modular Architecture**: Site profiles for easy addition of new e-commerce platforms

## 🏗️ Architecture

The system uses a **LangChain agent architecture** with specialized tools:

- **🎯 Intent Extraction Tool**: Uses GPT-3.5 to understand user requests
- **🌐 Web Scraping Tool**: Playwright-based scraping with dynamic element detection
- **📦 Product Processing Tool**: Filters, ranks, and formats product data
- **💬 Conversational Tool**: Handles non-product queries in user's language
- **📱 WhatsApp Sender Tool**: Manages message delivery with media support

## 📁 Project Structure

```
├── core/                           # Core application code
│   ├── agent.py                    # Main LangChain agent orchestrator
│   ├── endpoint.py                 # FastAPI endpoints for WhatsApp integration
│   ├── site_profiles.py            # Modular site configurations (Jiji, etc.)
│   ├── tools/                      # LangChain tools
│   │   ├── intent_extractor.py     # GPT-3.5 intent extraction
│   │   ├── web_scraper.py          # Playwright web scraping
│   │   ├── product_processor.py    # Product data processing
│   │   ├── conversational_responder.py # Non-product query handling
│   │   └── whatsapp_sender.py      # WhatsApp message sending
│   └── utils/                      # Utility modules
│       └── retry.py                # Retry logic and error handling
├── store/                          # Storage for scraped product data
│   └── products.json               # Product search results
├── test_agent.py                   # Comprehensive test suite
├── install.py                      # Installation script
├── requirements.txt                # Python dependencies
└── .env.example                    # Environment configuration template
```

## 🛠️ Technologies Used

- **🐍 Python 3.8+**: Primary programming language
- **🤖 LangChain**: Agent framework and tool orchestration
- **🧠 GPT-3.5-turbo**: Language model via OpenRouter API
- **🌐 Playwright**: Browser automation for web scraping
- **⚡ FastAPI**: Web framework for handling API requests
- **📱 Twilio**: WhatsApp messaging service
- **🔄 Retry Logic**: Robust error handling with exponential backoff

## 🚀 Getting Started

### Prerequisites

- **Python 3.8+**
- **Twilio account** with WhatsApp Business API access
- **OpenRouter API key** for GPT-3.5-turbo access

### 🔧 Quick Installation

1. **Clone the repository:**
   ```bash
   git clone [repository-url]
   cd dealer_ai
   ```

2. **Run the installation script:**
   ```bash
   python install.py
   ```

   This will:
   - Install all Python dependencies
   - Set up Playwright browsers
   - Create necessary directories
   - Set up environment configuration

3. **Manual installation (alternative):**
   ```bash
   pip install -r requirements.txt
   playwright install chromium
   ```

### ⚙️ Configuration

1. **Update environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your credentials
   ```

2. **Required environment variables:**
   ```env
   # Twilio Configuration
   TWILIO_SID=**********************************
   TWILIO_AUTH_TOKEN=c90c1ab2f98e5f33ddfaeff8d98fde69
   TWILIO_WHATSAPP_FROM=whatsapp:+***********
   TWILIO_WHATSAPP_TO=whatsapp:+************

   # OpenRouter API Configuration
   OPENROUTER_API_KEY=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
   ```

### 🏃‍♂️ Running the Application

1. **Start the FastAPI server:**
   ```bash
   uvicorn core.endpoint:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Test the agent:**
   ```bash
   python test_agent.py
   ```

3. **Health check:**
   ```bash
   curl http://localhost:8000/health
   ```

## 💬 Usage Examples

Users can interact with Dealer AI by sending WhatsApp messages. The agent handles both product requests and general conversation:

### Product Requests (Swahili):
- *"Nionyeshe simu za Samsung chini ya laki tano"*
- *"Nataka laptop ya Dell iliyotumika"*
- *"Vifaa vya kompyuta chini ya elfu hamsini"*

### Product Requests (English):
- *"Show me Samsung phones under 500,000 TZS"*
- *"I need a used Dell laptop"*
- *"Find electronics under 100,000 TZS"*

### General Conversation:
- *"Hujambo, habari za asubuhi?"* (Swahili greeting)
- *"What can you help me with?"* (English inquiry)
- *"Nifafanulie Dealer AI inafanya nini"* (What does Dealer AI do?)

## 🔧 How It Works

1. **Message Reception**: User sends WhatsApp message via Twilio webhook
2. **Intent Extraction**: GPT-3.5 analyzes message to extract product intent
3. **Decision Making**: LangChain agent decides on appropriate workflow
4. **Web Scraping**: If product request, Playwright scrapes Jiji.co.tz
5. **Product Processing**: Results are filtered, ranked, and formatted
6. **Response Generation**: GPT-3.5 creates user-friendly responses
7. **Message Delivery**: Formatted response sent back via WhatsApp

## 🧪 Testing

The project includes comprehensive tests:

```bash
# Run all tests
python test_agent.py

# Test specific endpoint
curl -X POST http://localhost:8000/test \
  -H "Content-Type: application/json" \
  -d '{"message": "Samsung phone", "phone": "+255000000000"}'
```

## 🔮 Future Enhancements

- **🌐 Multi-site Support**: Add Jumia, Kikuu, and other platforms
- **📊 Price Tracking**: Historical price data and alerts
- **👤 User Profiles**: Personalized recommendations
- **🔍 Advanced Filters**: Brand, specifications, location-based filtering
- **📈 Analytics**: Usage patterns and popular products
- **🤖 Voice Support**: Voice message processing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contact

[Add contact information here]