# Dealer AI - WhatsApp Product Search Assistant

Dealer AI is a WhatsApp-based AI assistant that helps users find product deals based on their preferences. The application allows users to search for products in different categories by sending WhatsApp messages in any language.

## Features

- **WhatsApp Integration**: Users can search for products by sending messages via WhatsApp
- **Natural Language Understanding**: Understands user queries in multiple languages
- **Product Scraping**: Searches for products on e-commerce platforms
- **AI-Powered Responses**: Uses GPT models to interpret queries and provide friendly responses
- **Product Categories**: Currently supports laptops, mobile phones, and computer accessories

## Architecture

The project consists of several components:

- **FastAPI Backend**: Processes incoming WhatsApp messages and coordinates responses
- **AI Model Integration**: Uses OpenRouter API to access language models for understanding user queries
- **Web Scraping**: Uses Playwright to scrape product details from e-commerce websites
- **Database**: SQL database for storing user information, product details, and query history
- **Twilio Integration**: For sending and receiving WhatsApp messages

## Project Structure

```
├── core/                   # Core application code
│   ├── endpoint.py         # FastAPI endpoints for WhatsApp integration
│   ├── model_reply.py      # AI model integration for understanding user queries
│   ├── scrap.py            # Web scraping functionality for product search
│   └── send_message.py     # Twilio integration for sending WhatsApp messages
├── db/                     # Database related files
│   └── dealer.sql          # SQL schema for the application
├── store/                  # Storage for scraped product data
│   └── products.json       # Temporary storage for product search results
└── requirements.txt        # Python dependencies
```

## Technologies Used

- **Python**: Primary programming language
- **FastAPI**: Web framework for handling API requests
- **Playwright**: Browser automation for web scraping
- **Twilio**: WhatsApp messaging service
- **OpenRouter API**: For accessing language models
- **SQL Database**: For data persistence

## Getting Started

### Prerequisites

- Python 3.8+
- A Twilio account with WhatsApp integration
- OpenRouter API access

### Installation

1. Clone the repository:
   ```
   git clone [repository-url]
   cd dealer-ai
   ```

2. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   Create a `.env` file in the root directory with the following variables:
   ```
   TWILIO_SID=your_twilio_sid
   TWILIO_AUTH_TOKEN=your_twilio_auth_token
   OPENROUTER_API_KEY=your_openrouter_api_key
   ```

4. Initialize the database using the schema in `db/dealer.sql`

### Running the Application

Start the FastAPI server:
```
uvicorn core.endpoint:app --reload
```

## Usage

Users can interact with Dealer AI by sending WhatsApp messages to the configured Twilio number. Examples:

- "I need a new Samsung phone under 500,000 TZS"
- "Used Dell laptop with i7 processor"
- "Computer accessories under 50,000 TZS"

The AI will process these requests, scrape relevant product information, and respond with product details and images.

## Future Enhancements

- Additional product categories
- More e-commerce platform integrations
- User preference tracking
- Price history and alerts
- Enhanced product filtering options

## License

[Add license information here]

## Contact

[Add contact information here]