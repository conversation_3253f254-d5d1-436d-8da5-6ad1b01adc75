#!/usr/bin/env python3
"""
Demo script for Dealer AI
Demonstrates the complete autonomous agent workflow
"""

import asyncio
import json
import sys
import os
from dotenv import load_dotenv

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.agent import process_whatsapp_message

load_dotenv()

# Demo scenarios
DEMO_SCENARIOS = [
    {
        "title": "🇹🇿 Swahili Product Request",
        "message": "Nionyeshe simu za Samsung chini ya laki tano",
        "description": "User asks for Samsung phones under 500,000 TZS in Swahili"
    },
    {
        "title": "🇬🇧 English Product Request", 
        "message": "Show me used Dell laptops",
        "description": "User asks for used Dell laptops in English"
    },
    {
        "title": "🇹🇿 Swahili Greeting",
        "message": "Hujambo, habari za asubuhi?",
        "description": "User greets in Swahili - should get conversational response"
    },
    {
        "title": "🇬🇧 English Capability Inquiry",
        "message": "What can you help me with?",
        "description": "User asks about capabilities - should explain Dealer AI"
    },
    {
        "title": "🔍 Simple Product Search",
        "message": "Samsung phone",
        "description": "Simple product request - should trigger search"
    }
]

async def run_demo_scenario(scenario, index, total):
    """Run a single demo scenario"""
    print(f"\n{'='*60}")
    print(f"Demo {index}/{total}: {scenario['title']}")
    print(f"{'='*60}")
    print(f"📝 Description: {scenario['description']}")
    print(f"💬 User Message: '{scenario['message']}'")
    print(f"🤖 Processing...")
    
    try:
        # Process the message
        result = await process_whatsapp_message(scenario['message'], "+************")
        
        # Display results
        if result.get("success"):
            print(f"✅ Status: Success")
            response = result.get("response", "")
            
            # Truncate long responses for demo
            if len(response) > 200:
                display_response = response[:200] + "..."
            else:
                display_response = response
                
            print(f"📤 Response: {display_response}")
            
            # Show intermediate steps if available
            steps = result.get("intermediate_steps", [])
            if steps:
                print(f"🔧 Tools Used: {len(steps)} steps")
                for i, step in enumerate(steps[:3], 1):  # Show first 3 steps
                    if hasattr(step, 'tool') and hasattr(step, 'tool_input'):
                        print(f"   {i}. {step.tool}")
        else:
            print(f"❌ Status: Failed")
            print(f"🚨 Error: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
    
    print(f"⏱️  Scenario completed")

async def run_full_demo():
    """Run the complete demo"""
    print("🚀 Dealer AI - Autonomous Agent Demo")
    print("🤖 Showcasing LangChain + GPT-3.5 + Playwright Integration")
    print(f"📊 Running {len(DEMO_SCENARIOS)} scenarios...")
    
    # Check environment
    required_vars = ["OPENROUTER_API_KEY", "TWILIO_SID", "TWILIO_AUTH_TOKEN"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"\n❌ Missing environment variables: {', '.join(missing_vars)}")
        print("Please check your .env file and try again.")
        return
    
    print("✅ Environment variables configured")
    
    # Run scenarios
    for i, scenario in enumerate(DEMO_SCENARIOS, 1):
        await run_demo_scenario(scenario, i, len(DEMO_SCENARIOS))
        
        # Pause between scenarios
        if i < len(DEMO_SCENARIOS):
            print(f"\n⏳ Waiting 3 seconds before next scenario...")
            await asyncio.sleep(3)
    
    # Final summary
    print(f"\n{'='*60}")
    print("🎉 Demo Completed!")
    print(f"{'='*60}")
    print("📋 What was demonstrated:")
    print("✅ Autonomous LangChain agent decision-making")
    print("✅ Multi-language support (Swahili & English)")
    print("✅ Intent extraction with GPT-3.5")
    print("✅ Dynamic web scraping with Playwright")
    print("✅ Conversational vs. product request routing")
    print("✅ WhatsApp-ready response formatting")
    print("✅ Error handling and retry logic")
    
    print(f"\n🌟 Your Dealer AI is now a fully autonomous agent!")
    print("🚀 Ready for production deployment")

async def quick_test():
    """Quick test of a single message"""
    print("🧪 Quick Test - Single Message")
    print("="*40)
    
    test_message = "Samsung phone under 400000"
    print(f"Testing: '{test_message}'")
    
    try:
        result = await process_whatsapp_message(test_message, "+************")
        
        if result.get("success"):
            print("✅ Test passed!")
            print(f"Response length: {len(result.get('response', ''))}")
        else:
            print("❌ Test failed!")
            print(f"Error: {result.get('error', 'Unknown')}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] == "quick":
        print("🏃‍♂️ Running quick test...")
        asyncio.run(quick_test())
    else:
        print("🎬 Running full demo...")
        asyncio.run(run_full_demo())

if __name__ == "__main__":
    main()
