#!/usr/bin/env python3
"""
Installation script for Dealer AI
Installs dependencies and sets up the environment
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    print("🔍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible")
        print("   Dealer AI requires Python 3.8 or higher")
        return False

def install_dependencies():
    """Install Python dependencies"""
    print("📦 Installing Python dependencies...")
    
    # Upgrade pip first
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip"):
        return False
    
    # Install requirements
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "Installing requirements"):
        return False
    
    return True

def install_playwright():
    """Install Playwright browsers"""
    print("🌐 Installing Playwright browsers...")
    
    if not run_command("playwright install chromium", "Installing Chromium browser"):
        return False
    
    return True

def setup_environment():
    """Set up environment file"""
    print("⚙️  Setting up environment...")
    
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if env_example.exists() and not env_file.exists():
        try:
            # Copy .env.example to .env
            with open(env_example, 'r') as src, open(env_file, 'w') as dst:
                dst.write(src.read())
            print("✅ Created .env file from .env.example")
            print("⚠️  Please update .env file with your actual credentials")
            return True
        except Exception as e:
            print(f"❌ Failed to create .env file: {e}")
            return False
    elif env_file.exists():
        print("✅ .env file already exists")
        return True
    else:
        print("❌ .env.example file not found")
        return False

def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")
    
    directories = ["store", "logs"]
    
    for directory in directories:
        try:
            Path(directory).mkdir(exist_ok=True)
            print(f"✅ Created/verified directory: {directory}")
        except Exception as e:
            print(f"❌ Failed to create directory {directory}: {e}")
            return False
    
    return True

def verify_installation():
    """Verify that installation was successful"""
    print("🔍 Verifying installation...")
    
    try:
        # Try importing key modules
        import langchain
        import playwright
        import twilio
        import fastapi
        print("✅ All key modules can be imported")
        
        # Check if Chromium is installed
        result = subprocess.run("playwright install --dry-run chromium", 
                              shell=True, capture_output=True, text=True)
        if "is already installed" in result.stdout or result.returncode == 0:
            print("✅ Playwright Chromium browser is available")
        else:
            print("⚠️  Playwright Chromium browser may not be installed properly")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

def main():
    """Main installation function"""
    print("🚀 Dealer AI Installation Script")
    print("="*50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    if not create_directories():
        print("❌ Failed to create directories")
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        sys.exit(1)
    
    # Install Playwright
    if not install_playwright():
        print("⚠️  Playwright installation failed, but continuing...")
        print("   You may need to run 'playwright install chromium' manually")
    
    # Setup environment
    if not setup_environment():
        print("❌ Failed to setup environment")
        sys.exit(1)
    
    # Verify installation
    if not verify_installation():
        print("⚠️  Installation verification failed")
        print("   Some components may not work properly")
    
    print("\n🎉 Installation completed!")
    print("\n📋 Next steps:")
    print("1. Update the .env file with your actual credentials:")
    print("   - TWILIO_SID")
    print("   - TWILIO_AUTH_TOKEN") 
    print("   - OPENROUTER_API_KEY")
    print("2. Test the installation: python test_agent.py")
    print("3. Start the server: uvicorn core.endpoint:app --reload")
    print("\n💡 For help, check the README.md file")

if __name__ == "__main__":
    main()
