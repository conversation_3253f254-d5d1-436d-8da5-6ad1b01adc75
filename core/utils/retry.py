"""
Retry utilities for Dealer AI
Provides robust retry mechanisms with exponential backoff
"""

import asyncio
import time
import logging
from typing import Callable, Any, Optional, Type, Union, List
from functools import wraps

logger = logging.getLogger(__name__)

class RetryError(Exception):
    """Exception raised when all retry attempts are exhausted"""
    pass

def retry_with_backoff(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exponential_base: float = 2.0,
    exceptions: Union[Type[Exception], tuple] = Exception,
    on_retry: Optional[Callable] = None
):
    """
    Decorator for retrying functions with exponential backoff
    
    Args:
        max_attempts: Maximum number of retry attempts
        base_delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries in seconds
        exponential_base: Base for exponential backoff calculation
        exceptions: Exception types to catch and retry on
        on_retry: Optional callback function called on each retry
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    if asyncio.iscoroutinefunction(func):
                        return await func(*args, **kwargs)
                    else:
                        return func(*args, **kwargs)
                        
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        # Last attempt failed
                        logger.error(f"All {max_attempts} attempts failed for {func.__name__}: {e}")
                        raise RetryError(f"Function {func.__name__} failed after {max_attempts} attempts") from e
                    
                    # Calculate delay with exponential backoff
                    delay = min(base_delay * (exponential_base ** attempt), max_delay)
                    
                    logger.warning(f"Attempt {attempt + 1}/{max_attempts} failed for {func.__name__}: {e}. Retrying in {delay:.2f}s")
                    
                    # Call retry callback if provided
                    if on_retry:
                        try:
                            if asyncio.iscoroutinefunction(on_retry):
                                await on_retry(attempt, e, delay)
                            else:
                                on_retry(attempt, e, delay)
                        except Exception as callback_error:
                            logger.error(f"Retry callback failed: {callback_error}")
                    
                    await asyncio.sleep(delay)
            
            # This should never be reached, but just in case
            raise RetryError(f"Unexpected error in retry logic for {func.__name__}") from last_exception
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                        
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        # Last attempt failed
                        logger.error(f"All {max_attempts} attempts failed for {func.__name__}: {e}")
                        raise RetryError(f"Function {func.__name__} failed after {max_attempts} attempts") from e
                    
                    # Calculate delay with exponential backoff
                    delay = min(base_delay * (exponential_base ** attempt), max_delay)
                    
                    logger.warning(f"Attempt {attempt + 1}/{max_attempts} failed for {func.__name__}: {e}. Retrying in {delay:.2f}s")
                    
                    # Call retry callback if provided
                    if on_retry:
                        try:
                            on_retry(attempt, e, delay)
                        except Exception as callback_error:
                            logger.error(f"Retry callback failed: {callback_error}")
                    
                    time.sleep(delay)
            
            # This should never be reached, but just in case
            raise RetryError(f"Unexpected error in retry logic for {func.__name__}") from last_exception
        
        # Return appropriate wrapper based on whether function is async
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

async def retry_async_operation(
    operation: Callable,
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exceptions: Union[Type[Exception], tuple] = Exception,
    *args,
    **kwargs
) -> Any:
    """
    Retry an async operation with exponential backoff
    
    Args:
        operation: The async function to retry
        max_attempts: Maximum number of retry attempts
        base_delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries in seconds
        exceptions: Exception types to catch and retry on
        *args, **kwargs: Arguments to pass to the operation
    
    Returns:
        Result of the operation
        
    Raises:
        RetryError: If all attempts fail
    """
    last_exception = None
    
    for attempt in range(max_attempts):
        try:
            return await operation(*args, **kwargs)
            
        except exceptions as e:
            last_exception = e
            
            if attempt == max_attempts - 1:
                logger.error(f"All {max_attempts} attempts failed: {e}")
                raise RetryError(f"Operation failed after {max_attempts} attempts") from e
            
            delay = min(base_delay * (2 ** attempt), max_delay)
            logger.warning(f"Attempt {attempt + 1}/{max_attempts} failed: {e}. Retrying in {delay:.2f}s")
            await asyncio.sleep(delay)
    
    raise RetryError("Unexpected error in retry logic") from last_exception

def circuit_breaker(
    failure_threshold: int = 5,
    recovery_timeout: float = 60.0,
    expected_exception: Type[Exception] = Exception
):
    """
    Circuit breaker decorator to prevent cascading failures
    
    Args:
        failure_threshold: Number of failures before opening circuit
        recovery_timeout: Time to wait before attempting recovery
        expected_exception: Exception type that triggers circuit breaker
    """
    def decorator(func: Callable) -> Callable:
        func._circuit_breaker_failures = 0
        func._circuit_breaker_last_failure_time = 0
        func._circuit_breaker_state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            current_time = time.time()
            
            # Check if circuit should be half-open (recovery attempt)
            if (func._circuit_breaker_state == "OPEN" and 
                current_time - func._circuit_breaker_last_failure_time > recovery_timeout):
                func._circuit_breaker_state = "HALF_OPEN"
                logger.info(f"Circuit breaker for {func.__name__} entering HALF_OPEN state")
            
            # If circuit is open, fail fast
            if func._circuit_breaker_state == "OPEN":
                raise RetryError(f"Circuit breaker is OPEN for {func.__name__}")
            
            try:
                result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
                
                # Success - reset circuit breaker
                if func._circuit_breaker_state == "HALF_OPEN":
                    func._circuit_breaker_state = "CLOSED"
                    func._circuit_breaker_failures = 0
                    logger.info(f"Circuit breaker for {func.__name__} reset to CLOSED state")
                
                return result
                
            except expected_exception as e:
                func._circuit_breaker_failures += 1
                func._circuit_breaker_last_failure_time = current_time
                
                if func._circuit_breaker_failures >= failure_threshold:
                    func._circuit_breaker_state = "OPEN"
                    logger.error(f"Circuit breaker for {func.__name__} opened after {failure_threshold} failures")
                
                raise e
        
        return wrapper
    return decorator
