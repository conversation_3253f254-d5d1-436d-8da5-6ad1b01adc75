import os
import json
from playwright.async_api import async_playwright

user_data_dir = os.path.expanduser("~/.playwright_profile/chrome")

async def deal_scraper(prompt):
    """prompt must be a json or dictionary"""

    async with async_playwright() as p:
        browser = await p.chromium.launch_persistent_context(
            user_data_dir=user_data_dir,
            headless=True,
            viewport={"width": 1920, "height": 1080}
        )

        page = await browser.new_page()

        base_url = "https://mykariakoo.co.tz"

        if isinstance(prompt, str):
            prompt = json.loads(prompt)

        expected_keys = ['category', 'brand', 'price', 'max_price', 'min_price', 'condition']

        path_segments = []
        for key in expected_keys:
            value = prompt.get(key)
            if value is not None and value != "N/A":
                path_segments.append(value)

        url = f"{base_url}/collections" + "/" + "/".join(path_segments)
        print(f"PAth segs: {path_segments}\nUrl:{url}")
        await page.goto(url, wait_until="load")

        if await page.locator(".empty-state__heading.heading").is_visible():
            info = "Sorry, could not find what you asked for...! Please refine your prompt."
            print(info)
            await browser.close()
            return {"info": info}
        else:
            await page.wait_for_timeout(timeout=2000)

            products = page.locator(".product-item.product-item--vertical")

            num_products = await products.count()

            print(f"Found {num_products} items")

            scrapped = []
            for i in range(num_products):
                product = products.nth(i)

                print(f"\nProcessing product item {i + 1}...")

                await product.scroll_into_view_if_needed()

                image_locator = product.locator("img.product-item__primary-image").first
                
                image = (await image_locator.get_attribute("srcset")).split(',')[0].split(' ')[0] if await image_locator.count() > 0 else "N/A - Image not found"

                info_locator = product.locator(".product-item__info").first

                price_locator = info_locator.locator(".price.price--highlight").first
                price = await price_locator.inner_text() if await price_locator.count() > 0 else "N/A - Price not found"

                old_price_locator = info_locator.locator(".price.price--compare").first
                old_price = await old_price_locator.inner_text() if await old_price_locator.count() > 0 else "N/A - Old price not found"

                link_locator = info_locator.locator("a").first
                link = f"{base_url}{await link_locator.get_attribute('href')}" if await link_locator.count() > 0 and await link_locator.get_attribute('href') else "N/A - Link not found"

                description_locator = info_locator.locator("a").first
                description = await description_locator.inner_text() if await description_locator.count() > 0 else "N/A - Description not found"

                scrapped.append({
                    "image": f"https:{image}",
                    "price": price,
                    "old_price": old_price,
                    "link": link,
                    "description": description
                })

            os.makedirs("store", exist_ok=True)
            with open("store/products.json", "w", encoding="utf-8") as f:
                json.dump(scrapped, f, ensure_ascii=False, indent=4)

            print("Done!")
            return {"info": "Done"}

if __name__ == "__main__":
    import asyncio
    asyncio.run(deal_scraper(prompt={"category": "laptops", "brand": "dell"}))
