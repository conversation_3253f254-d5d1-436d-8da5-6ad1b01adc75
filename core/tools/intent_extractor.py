"""
Intent Extraction Tool for Dealer AI
Uses GPT-3.5 to extract structured intent from user messages
"""

import json
import os
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field

from langchain_core.tools import BaseTool
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from dotenv import load_dotenv

load_dotenv()

class IntentExtractionInput(BaseModel):
    """Input schema for intent extraction"""
    user_message: str = Field(description="The user's message to analyze")
    user_language: Optional[str] = Field(default=None, description="Detected or preferred language")

class IntentExtractionTool(BaseTool):
    """
    Tool that extracts structured intent from user messages using GPT-3.5
    Determines if message is about product deals or general conversation
    """
    
    name: str = "intent_extractor"
    description: str = """
    Extract structured intent from user messages. Use this tool to understand what the user wants.
    Returns structured data including:
    - category: product category or 'general' for non-product queries
    - product_name: specific product mentioned
    - max_price: maximum price if mentioned
    - condition: new/used/refurbished preference
    - site_preference: preferred site if mentioned
    - language: detected language of the message
    - is_product_request: boolean indicating if this is about finding products
    """
    args_schema = IntentExtractionInput
    
    def __init__(self):
        super().__init__()
        self.llm = ChatOpenAI(
            model="openai/gpt-3.5-turbo",
            openai_api_key=os.getenv("OPENROUTER_API_KEY"),
            openai_api_base="https://openrouter.ai/api/v1",
            temperature=0.1,
            max_tokens=1000
        )
    
    def _run(self, user_message: str, user_language: Optional[str] = None) -> str:
        """Extract intent from user message"""
        try:
            system_prompt = """
You are an expert intent extraction system for Dealer AI, a WhatsApp shopping assistant.

Analyze the user's message and extract structured information. The user might be:
1. Looking for product deals (phones, laptops, electronics, etc.)
2. Asking general questions about Dealer AI or having casual conversation

PRODUCT CATEGORIES TO RECOGNIZE:
- mobile-phones (smartphones, phones, simu, etc)
- laptops (computers, kompyuta, etc)
- electronics (headphones, chargers, accessories, etc) and many more categories
- general (if product type unclear but seems like shopping request)

EXTRACTION RULES:
- Extract product name/type from the message
- Look for price mentions (maximum budget, price range)
- Detect condition preferences (new, used, mpya, iliyotumika)
- Check for site preferences (jiji, jumia, etc.)
- Detect the language (swahili, english, etc.)
- Determine if this is a product request or general conversation

EXAMPLES:
"Nionyeshe simu za Samsung chini ya laki tano" → product request
"Nifafanulie Dealer AI inafanya nini" → general conversation
"I need a Dell laptop under $800" → product request
"Hello, how are you?" → general conversation

Return ONLY a JSON object with this exact structure:
{
    "category": "mobile-phones|laptops|electronics|general",
    "product_name": "extracted product name or null",
    "max_price": "extracted price number only or null",
    "condition": "new|used|null",
    "site_preference": "jiji|jumia|null",
    "language": "swahili|english|other",
    "is_product_request": true/false,
    "confidence": 0.0-1.0
}
"""
            
            user_prompt = f"Analyze this message: '{user_message}'"
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = self.llm.invoke(messages)
            
            # Parse the JSON response
            try:
                intent_data = json.loads(response.content.strip())
                return json.dumps(intent_data, ensure_ascii=False)
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                return json.dumps({
                    "category": "general",
                    "product_name": None,
                    "max_price": None,
                    "condition": None,
                    "site_preference": None,
                    "language": "unknown",
                    "is_product_request": False,
                    "confidence": 0.5,
                    "error": "Failed to parse LLM response"
                })
                
        except Exception as e:
            return json.dumps({
                "category": "general",
                "product_name": None,
                "max_price": None,
                "condition": None,
                "site_preference": None,
                "language": "unknown",
                "is_product_request": False,
                "confidence": 0.0,
                "error": f"Intent extraction failed: {str(e)}"
            })
    
    async def _arun(self, user_message: str, user_language: Optional[str] = None) -> str:
        """Async version of intent extraction"""
        try:
            system_prompt = """
You are an expert intent extraction system for Dealer AI, a WhatsApp shopping assistant.

Analyze the user's message and extract structured information. The user might be:
1. Looking for product deals (phones, laptops, electronics, etc.)
2. Asking general questions about Dealer AI or having casual conversation

PRODUCT CATEGORIES TO RECOGNIZE:
- mobile-phones (smartphones, phones, simu)
- laptops (computers, kompyuta)
- electronics (headphones, chargers, accessories)
- general (if product type unclear but seems like shopping request)

EXTRACTION RULES:
- Extract product name/type from the message
- Look for price mentions (maximum budget, price range)
- Detect condition preferences (new, used, mpya, iliyotumika)
- Check for site preferences (jiji, jumia, etc.)
- Detect the language (swahili, english, etc.)
- Determine if this is a product request or general conversation

Return ONLY a JSON object with this exact structure:
{
    "category": "mobile-phones|laptops|electronics|general",
    "product_name": "extracted product name or null",
    "max_price": "extracted price number only or null",
    "condition": "new|used|null",
    "site_preference": "jiji|jumia|null",
    "language": "swahili|english|other",
    "is_product_request": true/false,
    "confidence": 0.0-1.0
}
"""
            
            user_prompt = f"Analyze this message: '{user_message}'"
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            
            # Parse the JSON response
            try:
                intent_data = json.loads(response.content.strip())
                return json.dumps(intent_data, ensure_ascii=False)
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                return json.dumps({
                    "category": "general",
                    "product_name": None,
                    "max_price": None,
                    "condition": None,
                    "site_preference": None,
                    "language": "unknown",
                    "is_product_request": False,
                    "confidence": 0.5,
                    "error": "Failed to parse LLM response"
                })
                
        except Exception as e:
            return json.dumps({
                "category": "general",
                "product_name": None,
                "max_price": None,
                "condition": None,
                "site_preference": None,
                "language": "unknown",
                "is_product_request": False,
                "confidence": 0.0,
                "error": f"Intent extraction failed: {str(e)}"
            })
