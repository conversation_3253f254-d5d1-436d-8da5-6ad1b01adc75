"""
Product Processing Tool for Dealer AI
Handles product data processing, ranking, and WhatsApp formatting
"""

import json
import os
from typing import Dict, Any, List, Optional, Type
from pydantic import BaseModel, Field

from langchain_core.tools import BaseTool
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from dotenv import load_dotenv

load_dotenv()

class ProductProcessingInput(BaseModel):
    """Input schema for product processing"""
    scraped_data: str = Field(description="JSON string containing scraped product data")
    intent_data: str = Field(description="JSON string containing user intent data")
    max_products: Optional[int] = Field(default=10, description="Maximum number of products to return")

class ProductProcessingTool(BaseTool):
    """
    Tool that processes scraped product data and formats it for WhatsApp responses
    """
    
    name: str = "product_processor"
    description: str = """
    Process and format scraped product data for WhatsApp responses. Use this tool after
    web scraping to:
    1. Filter and rank products based on user preferences
    2. Save products to JSON file
    3. Format top products for WhatsApp messages
    4. Generate user-friendly product descriptions
    
    Provide scraped data and intent data as JSON strings.
    """
    args_schema: Type[BaseModel] = ProductProcessingInput
    
    @property
    def store_dir(self):
        return "store"

    @property
    def products_file(self):
        return "products.json"

    def _get_llm(self):
        """Get LLM instance"""
        return ChatOpenAI(
            model="openai/gpt-3.5-turbo",
            openai_api_key=os.getenv("OPENROUTER_API_KEY"),
            openai_api_base="https://openrouter.ai/api/v1",
            temperature=0.3,
            max_tokens=2000
        )
    
    def _run(self, scraped_data: str, intent_data: str, max_products: int = 10) -> str:
        """Process scraped product data"""
        try:
            # Parse input data
            scraped = json.loads(scraped_data)
            intent = json.loads(intent_data)
            
            if not scraped.get("success", False):
                return json.dumps({
                    "success": False,
                    "error": "No valid scraped data provided",
                    "formatted_products": []
                })
            
            products = scraped.get("products", [])
            if not products:
                return json.dumps({
                    "success": False,
                    "error": "No products found in scraped data",
                    "formatted_products": []
                })
            
            # Process and rank products
            processed_products = self._process_products(products, intent)
            
            # Select top products
            top_products = processed_products[:max_products]
            
            # Save to JSON file
            self._save_products_to_file(processed_products)
            
            # Format for WhatsApp
            formatted_products = self._format_for_whatsapp(top_products, intent)
            
            return json.dumps({
                "success": True,
                "total_products": len(processed_products),
                "selected_products": len(top_products),
                "formatted_products": formatted_products,
                "saved_to_file": True
            }, ensure_ascii=False)
            
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"Product processing failed: {str(e)}",
                "formatted_products": []
            })
    
    async def _arun(self, scraped_data: str, intent_data: str, max_products: int = 10) -> str:
        """Async version of product processing"""
        return self._run(scraped_data, intent_data, max_products)
    
    def _process_products(self, products: List[Dict[str, Any]], intent: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Process and rank products based on user preferences"""
        processed = []
        max_price = intent.get("max_price")
        
        for product in products:
            try:
                # Clean and standardize product data
                processed_product = self._clean_product_data(product)
                
                # Add ranking score
                score = self._calculate_product_score(processed_product, intent)
                processed_product["score"] = score
                
                # Filter by price if specified
                if max_price and processed_product.get("price_numeric"):
                    if processed_product["price_numeric"] > float(max_price):
                        continue
                
                processed.append(processed_product)
                
            except Exception as e:
                print(f"Failed to process product: {e}")
                continue
        
        # Sort by score (highest first)
        processed.sort(key=lambda x: x.get("score", 0), reverse=True)
        
        return processed
    
    def _clean_product_data(self, product: Dict[str, Any]) -> Dict[str, Any]:
        """Clean and standardize product data"""
        cleaned = {
            "title": self._clean_text(product.get("title", "N/A")),
            "price": self._clean_text(product.get("price", "N/A")),
            "image": product.get("image", "N/A"),
            "link": product.get("link", "N/A"),
            "location": self._clean_text(product.get("location", "N/A"))
        }
        
        # Extract numeric price
        cleaned["price_numeric"] = self._extract_price_number(cleaned["price"])
        
        # Generate short description
        cleaned["description"] = self._generate_short_description(cleaned["title"])
        
        return cleaned
    
    def _clean_text(self, text: str) -> str:
        """Clean text data"""
        if not text or text == "N/A":
            return "N/A"
        
        # Remove extra whitespace and newlines
        cleaned = " ".join(text.strip().split())
        
        return cleaned
    
    def _extract_price_number(self, price_text: str) -> Optional[float]:
        """Extract numeric price from price text"""
        if not price_text or price_text == "N/A":
            return None
        
        try:
            # Remove common currency symbols and text
            import re
            numbers = re.findall(r'[\d,]+', price_text.replace(',', ''))
            if numbers:
                return float(numbers[0])
        except:
            pass
        
        return None
    
    def _generate_short_description(self, title: str) -> str:
        """Generate a short description from the title"""
        if not title or title == "N/A":
            return "Product details not available"
        
        # Truncate long titles
        if len(title) > 80:
            return title[:77] + "..."
        
        return title
    
    def _calculate_product_score(self, product: Dict[str, Any], intent: Dict[str, Any]) -> float:
        """Calculate relevance score for a product"""
        score = 0.0
        
        # Base score
        score += 1.0
        
        # Title relevance
        title = product.get("title", "").lower()
        product_name = intent.get("product_name", "").lower()
        
        if product_name and product_name in title:
            score += 2.0
        
        # Price availability
        if product.get("price_numeric"):
            score += 1.0
        
        # Image availability
        if product.get("image") and product["image"] != "N/A":
            score += 0.5
        
        # Link availability
        if product.get("link") and product["link"] != "N/A":
            score += 0.5
        
        return score
    
    def _save_products_to_file(self, products: List[Dict[str, Any]]):
        """Save products to JSON file"""
        try:
            os.makedirs(self.store_dir, exist_ok=True)
            file_path = os.path.join(self.store_dir, self.products_file)
            
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(products, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"Failed to save products to file: {e}")
    
    def _format_for_whatsapp(self, products: List[Dict[str, Any]], intent: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Format products for WhatsApp messages"""
        formatted = []
        
        for i, product in enumerate(products, 1):
            try:
                # Create WhatsApp-friendly format
                whatsapp_format = {
                    "rank": i,
                    "title": product.get("title", "N/A"),
                    "price": product.get("price", "N/A"),
                    "description": product.get("description", "N/A"),
                    "image": product.get("image", "N/A"),
                    "link": product.get("link", "N/A"),
                    "location": product.get("location", "N/A"),
                    "whatsapp_message": self._create_whatsapp_message(product, i)
                }
                
                formatted.append(whatsapp_format)
                
            except Exception as e:
                print(f"Failed to format product {i}: {e}")
                continue
        
        return formatted
    
    def _create_whatsapp_message(self, product: Dict[str, Any], rank: int) -> str:
        """Create a WhatsApp message for a single product"""
        try:
            title = product.get("title", "Product")
            price = product.get("price", "Price not available")
            location = product.get("location", "")
            link = product.get("link", "")
            
            # Create message
            message_parts = [
                f"🌟 *Deal #{rank}*",
                f"📱 {title}",
                f"💰 {price}"
            ]
            
            if location and location != "N/A":
                message_parts.append(f"📍 {location}")
            
            if link and link != "N/A":
                message_parts.append(f"🔗 View: {link}")
            
            return "\n".join(message_parts)
            
        except Exception as e:
            return f"🌟 *Deal #{rank}*\n📱 Product details not available"
