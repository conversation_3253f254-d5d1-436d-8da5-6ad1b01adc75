"""
Conversational Response Tool for Dealer AI
Handles non-product queries with contextual responses in user's language
"""

import json
import os
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field

from langchain_core.tools import BaseTool
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from dotenv import load_dotenv

load_dotenv()

class ConversationalInput(BaseModel):
    """Input schema for conversational responses"""
    user_message: str = Field(description="The user's message to respond to")
    intent_data: str = Field(description="JSON string containing extracted intent data")
    context: Optional[str] = Field(default=None, description="Additional context for the response")

class ConversationalResponderTool(BaseTool):
    """
    Tool that generates conversational responses for non-product queries
    Stays in character as Dealer AI and responds in the user's language
    """
    
    name: str = "conversational_responder"
    description: str = """
    Generate conversational responses for non-product queries. Use this tool when the user
    is asking general questions, greeting, or having casual conversation rather than looking
    for product deals. The tool will:
    1. Respond in the same language as the user
    2. Stay in character as Dealer AI
    3. Provide helpful information about Dealer AI's capabilities
    4. Guide users on how to search for products if appropriate
    """
    args_schema = ConversationalInput
    
    def __init__(self):
        super().__init__()
        self.llm = ChatOpenAI(
            model="openai/gpt-3.5-turbo",
            openai_api_key=os.getenv("OPENROUTER_API_KEY"),
            openai_api_base="https://openrouter.ai/api/v1",
            temperature=0.7,  # Slightly higher temperature for more natural conversation
            max_tokens=1000
        )
    
    def _run(self, user_message: str, intent_data: str, context: Optional[str] = None) -> str:
        """Generate conversational response"""
        try:
            # Parse intent data
            intent = json.loads(intent_data)
            
            # Generate response based on intent and message
            response = self._generate_response(user_message, intent, context)
            
            return json.dumps({
                "success": True,
                "response": response,
                "language": intent.get("language", "unknown"),
                "message_type": "conversational"
            }, ensure_ascii=False)
            
        except Exception as e:
            # Fallback response
            fallback = self._generate_fallback_response(user_message)
            return json.dumps({
                "success": False,
                "error": f"Response generation failed: {str(e)}",
                "response": fallback,
                "message_type": "fallback"
            })
    
    async def _arun(self, user_message: str, intent_data: str, context: Optional[str] = None) -> str:
        """Async version of conversational response"""
        try:
            # Parse intent data
            intent = json.loads(intent_data)
            
            # Generate response based on intent and message
            response = await self._generate_response_async(user_message, intent, context)
            
            return json.dumps({
                "success": True,
                "response": response,
                "language": intent.get("language", "unknown"),
                "message_type": "conversational"
            }, ensure_ascii=False)
            
        except Exception as e:
            # Fallback response
            fallback = self._generate_fallback_response(user_message)
            return json.dumps({
                "success": False,
                "error": f"Response generation failed: {str(e)}",
                "response": fallback,
                "message_type": "fallback"
            })
    
    def _generate_response(self, user_message: str, intent: Dict[str, Any], context: Optional[str] = None) -> str:
        """Generate contextual response using GPT-3.5"""
        language = intent.get("language", "english")
        
        system_prompt = self._create_system_prompt(language, context)
        user_prompt = self._create_user_prompt(user_message, intent)
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        response = self.llm.invoke(messages)
        return response.content.strip()
    
    async def _generate_response_async(self, user_message: str, intent: Dict[str, Any], context: Optional[str] = None) -> str:
        """Generate contextual response using GPT-3.5 (async)"""
        language = intent.get("language", "english")
        
        system_prompt = self._create_system_prompt(language, context)
        user_prompt = self._create_user_prompt(user_message, intent)
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        response = await self.llm.ainvoke(messages)
        return response.content.strip()
    
    def _create_system_prompt(self, language: str, context: Optional[str] = None) -> str:
        """Create system prompt based on language and context"""
        
        base_prompt = """
You are Dealer AI, a friendly and helpful WhatsApp-based shopping assistant. Your main purpose is to help users find the best product deals across e-commerce platforms in Tanzania.

CORE IDENTITY:
- You are Dealer AI, an intelligent shopping assistant
- You help users find deals on products like phones, laptops, and electronics
- You search websites like Jiji.co.tz to find the best prices
- You are knowledgeable about Tanzanian e-commerce and pricing

CAPABILITIES YOU CAN MENTION:
- Search for products on Jiji.co.tz and other platforms
- Find deals based on user preferences (price, condition, brand)
- Compare prices across different sellers
- Provide product recommendations
- Help with product specifications and features

CONVERSATION RULES:
1. Always stay in character as Dealer AI
2. Be friendly, helpful, and professional
3. Never go off-topic from your core purpose (finding deals)
4. If asked about unrelated topics, politely redirect to your shopping assistance role
5. Provide helpful guidance on how to search for products

LANGUAGE INSTRUCTIONS:
"""
        
        if language.lower() == "swahili":
            base_prompt += """
- RESPOND IN SWAHILI (Kiswahili)
- Use natural, conversational Swahili
- Be culturally appropriate for Tanzanian context
- Use common Swahili greetings and expressions
"""
        elif language.lower() == "english":
            base_prompt += """
- RESPOND IN ENGLISH
- Use clear, simple English
- Be friendly and approachable
- Use appropriate tone for Tanzanian context
"""
        else:
            base_prompt += f"""
- RESPOND IN {language.upper()} if possible, otherwise use English
- Be culturally sensitive and appropriate
- Maintain a helpful and friendly tone
"""
        
        if context:
            base_prompt += f"\n\nADDITIONAL CONTEXT:\n{context}"
        
        return base_prompt
    
    def _create_user_prompt(self, user_message: str, intent: Dict[str, Any]) -> str:
        """Create user prompt with message and intent context"""
        
        prompt = f"User message: '{user_message}'\n\n"
        
        # Add intent context
        if intent.get("language"):
            prompt += f"Detected language: {intent['language']}\n"
        
        if intent.get("confidence"):
            prompt += f"Intent confidence: {intent['confidence']}\n"
        
        prompt += """
Based on the user's message, provide an appropriate response as Dealer AI. Consider:

1. If it's a greeting, respond warmly and introduce your capabilities
2. If they're asking about what you do, explain your role as a shopping assistant
3. If they want to know how to use you, provide clear examples
4. If it's casual conversation, be friendly but guide them toward your main purpose
5. If they seem confused, offer helpful guidance

Keep your response concise, helpful, and in the appropriate language.
"""
        
        return prompt
    
    def _generate_fallback_response(self, user_message: str) -> str:
        """Generate a simple fallback response"""
        
        # Try to detect language from common patterns
        message_lower = user_message.lower()
        
        if any(word in message_lower for word in ["hujambo", "mambo", "habari", "salama", "nzuri"]):
            # Likely Swahili
            return """Hujambo! Mimi ni Dealer AI, msaidizi wako wa ununuzi kupitia WhatsApp. 

Ninaweza kukusaidia kupata bidhaa bora kwa bei nafuu kutoka Jiji.co.tz na maeneo mengine. 

Kwa mfano, unaweza kuniambia: "Nionyeshe simu za Samsung chini ya laki tano" au "Nataka laptop ya Dell iliyotumika."

Je, una kitu kingine unachotaka kutafuta?"""
        
        else:
            # Default to English
            return """Hello! I'm Dealer AI, your WhatsApp shopping assistant.

I help you find the best product deals from Jiji.co.tz and other e-commerce platforms in Tanzania.

You can ask me things like:
- "Show me Samsung phones under 500,000 TZS"
- "I need a used Dell laptop"
- "Find me electronics under 100,000 TZS"

What would you like to search for today?"""
