"""
WhatsApp Sender Tool for Dealer AI
Handles sending messages and media through Twilio WhatsApp API
"""

import json
import os
from typing import Dict, Any, Optional, List, Type
from pydantic import BaseModel, Field

from langchain_core.tools import BaseTool
from twilio.rest import Client
from dotenv import load_dotenv

load_dotenv()

class WhatsAppSenderInput(BaseModel):
    """Input schema for WhatsApp message sending"""
    message_data: str = Field(description="JSON string containing message data to send")
    recipient: Optional[str] = Field(default=None, description="Recipient phone number (optional)")
    message_type: Optional[str] = Field(default="text", description="Type of message: text, product, or media")

class WhatsAppSenderTool(BaseTool):
    """
    Tool that sends messages through WhatsApp using Twilio API
    Handles text messages, product information, and media
    """
    
    name: str = "whatsapp_sender"
    description: str = """
    Send messages through WhatsApp using Twilio API. Use this tool to send responses back to users.
    Can handle:
    1. Simple text messages
    2. Product information with images
    3. Formatted deal alerts
    4. Error messages and notifications
    
    Provide message data as JSON string containing the content to send.
    """
    args_schema: Type[BaseModel] = WhatsAppSenderInput
    
    @property
    def account_sid(self):
        return os.getenv("TWILIO_SID")

    @property
    def auth_token(self):
        return os.getenv("TWILIO_AUTH_TOKEN")

    @property
    def from_number(self):
        return os.getenv("TWILIO_WHATSAPP_FROM", "whatsapp:+***********")

    @property
    def to_number(self):
        return os.getenv("TWILIO_WHATSAPP_TO", "whatsapp:+************")

    def _get_client(self):
        """Get Twilio client"""
        account_sid = self.account_sid
        auth_token = self.auth_token

        if not account_sid or not auth_token:
            raise ValueError("Twilio credentials not found in environment variables")

        return Client(account_sid, auth_token)
    
    def _run(self, message_data: str, recipient: Optional[str] = None, message_type: str = "text") -> str:
        """Send WhatsApp message"""
        try:
            # Parse message data
            data = json.loads(message_data)
            
            # Determine recipient
            to_number = recipient or self.to_number
            if not to_number.startswith("whatsapp:"):
                to_number = f"whatsapp:{to_number}"
            
            # Send message based on type
            if message_type == "product" or data.get("message_type") == "product":
                return self._send_product_messages(data, to_number)
            elif message_type == "conversational" or data.get("message_type") == "conversational":
                return self._send_conversational_message(data, to_number)
            else:
                return self._send_simple_message(data, to_number)
                
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"Failed to send WhatsApp message: {str(e)}",
                "message_sent": False
            })
    
    async def _arun(self, message_data: str, recipient: Optional[str] = None, message_type: str = "text") -> str:
        """Async version of WhatsApp message sending"""
        return self._run(message_data, recipient, message_type)
    
    def _send_simple_message(self, data: Dict[str, Any], to_number: str) -> str:
        """Send a simple text message"""
        try:
            message_text = data.get("response", data.get("message", ""))
            
            if not message_text:
                return json.dumps({
                    "success": False,
                    "error": "No message content provided",
                    "message_sent": False
                })
            
            message = self._get_client().messages.create(
                from_=self.from_number,
                body=message_text,
                to=to_number
            )
            
            return json.dumps({
                "success": True,
                "message_sid": message.sid,
                "message_sent": True,
                "message_type": "simple_text"
            })
            
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"Failed to send simple message: {str(e)}",
                "message_sent": False
            })
    
    def _send_conversational_message(self, data: Dict[str, Any], to_number: str) -> str:
        """Send a conversational response message"""
        try:
            response_text = data.get("response", "")
            
            if not response_text:
                return json.dumps({
                    "success": False,
                    "error": "No response text provided",
                    "message_sent": False
                })
            
            message = self._get_client().messages.create(
                from_=self.from_number,
                body=response_text,
                to=to_number
            )
            
            return json.dumps({
                "success": True,
                "message_sid": message.sid,
                "message_sent": True,
                "message_type": "conversational",
                "language": data.get("language", "unknown")
            })
            
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"Failed to send conversational message: {str(e)}",
                "message_sent": False
            })
    
    def _send_product_messages(self, data: Dict[str, Any], to_number: str) -> str:
        """Send product information messages"""
        try:
            formatted_products = data.get("formatted_products", [])
            
            if not formatted_products:
                # Send "no products found" message
                return self._send_no_products_message(to_number)
            
            # Send products (limit to top 3 to avoid spam)
            sent_messages = []
            max_products = min(len(formatted_products), 3)
            
            for i in range(max_products):
                product = formatted_products[i]
                message_result = self._send_single_product(product, to_number)
                sent_messages.append(message_result)
            
            # Send summary if more products available
            if len(formatted_products) > max_products:
                summary_text = f"📊 Found {len(formatted_products)} total products. Showing top {max_products}. Need more options? Just ask!"
                
                summary_message = self._get_client().messages.create(
                    from_=self.from_number,
                    body=summary_text,
                    to=to_number
                )
                sent_messages.append({"message_sid": summary_message.sid, "type": "summary"})
            
            return json.dumps({
                "success": True,
                "messages_sent": len(sent_messages),
                "message_details": sent_messages,
                "total_products": len(formatted_products),
                "products_shown": max_products
            })
            
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"Failed to send product messages: {str(e)}",
                "message_sent": False
            })
    
    def _send_single_product(self, product: Dict[str, Any], to_number: str) -> Dict[str, Any]:
        """Send a single product message with image if available"""
        try:
            # Get product details
            whatsapp_message = product.get("whatsapp_message", "")
            image_url = product.get("image", "")
            
            # Create message parameters
            message_params = {
                "from_": self.from_number,
                "body": whatsapp_message,
                "to": to_number
            }
            
            # Add image if available and valid
            if image_url and image_url != "N/A" and self._is_valid_image_url(image_url):
                message_params["media_url"] = [image_url]
            
            # Send message
            message = self._get_client().messages.create(**message_params)
            
            return {
                "message_sid": message.sid,
                "type": "product",
                "has_image": bool(image_url and image_url != "N/A"),
                "product_title": product.get("title", "N/A")
            }
            
        except Exception as e:
            # Fallback: send without image
            try:
                fallback_message = self._get_client().messages.create(
                    from_=self.from_number,
                    body=product.get("whatsapp_message", "Product information not available"),
                    to=to_number
                )
                
                return {
                    "message_sid": fallback_message.sid,
                    "type": "product_fallback",
                    "error": f"Image failed: {str(e)}",
                    "product_title": product.get("title", "N/A")
                }
            except Exception as fallback_error:
                return {
                    "error": f"Complete failure: {str(fallback_error)}",
                    "type": "failed"
                }
    
    def _send_no_products_message(self, to_number: str) -> str:
        """Send message when no products are found"""
        try:
            no_products_text = """😔 Sorry, I couldn't find any products matching your request.

Try:
• Using different keywords
• Increasing your budget
• Checking for spelling
• Being less specific

Example: "Samsung phone under 400,000" instead of "Samsung Galaxy S21 Ultra 256GB"

What else can I help you find?"""
            
            message = self._get_client().messages.create(
                from_=self.from_number,
                body=no_products_text,
                to=to_number
            )
            
            return json.dumps({
                "success": True,
                "message_sid": message.sid,
                "message_sent": True,
                "message_type": "no_products"
            })
            
        except Exception as e:
            return json.dumps({
                "success": False,
                "error": f"Failed to send no products message: {str(e)}",
                "message_sent": False
            })
    
    def _is_valid_image_url(self, url: str) -> bool:
        """Check if URL is a valid image URL"""
        if not url or url == "N/A":
            return False
        
        # Basic URL validation
        if not url.startswith(("http://", "https://")):
            return False
        
        # Check for common image extensions
        image_extensions = ('.jpg', '.jpeg', '.png', '.gif', '.webp')
        url_lower = url.lower()
        
        return any(ext in url_lower for ext in image_extensions)
