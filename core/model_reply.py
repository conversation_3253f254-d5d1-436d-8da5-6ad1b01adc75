import os, json
import requests
from dotenv import load_dotenv

load_dotenv()

OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")

BASE_HEADERS = {
    "Authorization": f"Bearer {OPENROUTER_API_KEY}",
    "Content-Type": "application/json",
    "HTTP-Referer": "https://76e0-196-249-96-116.ngrok-free.app",  
    "X-Title": "Dealer AI"
}

API_URL = "https://openrouter.ai/api/v1/chat/completions"
MODEL = "openai/gpt-3.5-turbo"

# def gpt_reply(prompt: str) -> str:
#     try:
#         url = "https://openrouter.ai/api/v1/chat/completions"
#         headers = {
#             "Authorization": f"Bearer {OPENROUTER_API_KEY}",
#             "Content-Type": "application/json",
#             "HTTP-Referer": "https://76e0-196-249-96-116.ngrok-free.app",  
#             "X-Title": "Dealer AI"
#         }
#         data = {
#             "model": "openai/gpt-4",
#             "messages": [
#                 {
#                     "role": "system",
#                     "content": """
#                             You're Dealer AI, a friendly WhatsApp AI assistant that helps users find deals or in other words
#                             products via whatsapp prompts from any language. The product categories considered for now are only laptops,
#                             mobile phones, computer accessories. For laptops the brands available are dell and hp, 
#                             for mobile phones are infinix, samsung, tecno, smart phones and finally computer accessories
#                             in general(no brand for this). From the user's prompt analyze and extract the category of the 
#                             product prompted (ensure it is in the list of categories I have given you), brand also ensure
#                             it is in the list, price/min price/max price (this can be minimum price or maximum or just price
#                             depending on the user prompt), condition(new or used or refurbished), language detected( the language
#                             of the user's prompt you detected). Note that not all of these will be available in a single prompt, so just 
#                             extract what was found and the rest put as N/A. After extraction format them into a json with the keys being category, 
#                             brand, price or max_price or min_price, condition, language. You have to understand any language the prompt is given
#                             in then extract, but the formatted json should be in english.
#                             If a user gives a greeting or any prompt which is not a prompt for deals or products, reply refering that you are Dealer
#                             AI, do not go into more conversation with anything unrelated to your function as Dealer AI.
#                         """
#                 },
#                 {
#                     "role": "user",
#                     "content": prompt
#                 }
#             ]
#         }

#         response = requests.post(url, headers=headers, json=data)
#         response.raise_for_status()

#         reply = response.json()['choices'][0]['message']['content'].strip()
#         return reply

#     except Exception as e:
#         print(f"GPT Error: {e}")
#         return "Sorry, I had a little glitch. Please try again later!"


# 🧠 Function for structured extraction
def dealer_ai_extract(prompt: str) -> dict:
    try:
        system_prompt = """
        You are Dealer AI, a WhatsApp AI assistant helping users find deals. 
        The product categories considered for now are only laptops, mobile phones and computer accessories. 
        For laptops the brands available are dell and hp, for mobile phones are infinix, samsung, tecno, 
        smart phones and finally computer accessories in general(no brand for this). From the user's prompt 
        analyze and extract the category of the product prompted (ensure it is in the list of categories 
        I have given you), brand also ensure it is in the list, price/min price/max price (this can be minimum 
        price or maximum or just price depending on the user prompt), condition(new or used or refurbished), language detected( the language
        of the user's prompt you detected). Note that not all of these will be available in a single prompt, so just 
        extract what was found and the rest put as N/A, but category must be detected if not push as N/A also. 
        Extract the following details ONLY if the user's prompt is related to finding a product:
        - category (from: laptops, mobile phones, computer accessories)
        - brand (from: dell, hp, infinix, samsung, tecno, smart phones)
        - price, min_price or max_price (one of them depending on the prompt)
        - condition (new, used, refurbished)
        - language (language the user typed in)

        Return a JSON like this:
        {
            "category": "mobile-phones", (notice the -, you must use it here and in joining two or more words in these json responses)
            "brand": "samsung",
            "price": "500000",
            "condition": "used",
            "language": "swahili"
        }

        If the prompt isn't about product requests, return this exactly:
        {
            "category": "N/A",
            "brand": "N/A",
            "price": "N/A",
            "condition": "N/A",
            "language": "detected_language_here"
        }
        Note: All the keys in the returned json should be in lower case and if the value is N/A it must be in upper case MUST.
        """
        data = {
            "model": MODEL,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ]
        }

        response = requests.post(API_URL, headers=BASE_HEADERS, json=data)
        response.raise_for_status()

        reply_json = response.json()['choices'][0]['message']['content'].strip()
        return json.loads(reply_json)  
    
    except Exception as e:
        print(f"Extraction Error: {e}")
        return {
            "category": "N/A",
            "brand": "N/A",
            "price": "N/A",
            "condition": "N/A",
            "language": "N/A"
        }

# 💬 Function for general replies (e.g., greetings or unrelated messages)
def dealer_ai_reply(prompt: str) -> str:
    try:
        system_prompt = """
        You are Dealer AI, a WhatsApp assistant for helping users find deals on products. 
        If the user's message is a greeting or off-topic, politely let them know you're here 
        to help them find deals in laptops, mobile phones, and computer accessories. 
        Do not go off-topic or engage in off-topic talk, if they ask what's your name, or what
        you can do for them reply accordingly. Only engage in a way to always revolve around your
        core purpose (Dealer AI).
        If the user asked on how to prompt direct them, example "I want a samsung mobile phone,
        used from TZS 300,000 to TZS 500,000", now this example is just the base so use all examples 
        should align with it and change it to specific language as the user prompted with and the
        specific currency to the user's nationality (identify by using the language, default to dollar).
        For any incoveniences always refer that you only deal with Laptops, Mobile phones and Computer accessories
        Note: Answer you must answer in the respective language the user prompted with, it is must, again 
        I say must.
        """
        data = {
            "model": MODEL,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ]
        }

        response = requests.post(API_URL, headers=BASE_HEADERS, json=data)
        response.raise_for_status()
        return response.json()['choices'][0]['message']['content'].strip()

    except Exception as e:
        print(f"Reply Error: {e}")
        return "Sorry, I had a little glitch. Please try again later!"
    
