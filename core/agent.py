"""
Dealer AI - Autonomous LangChain Agent
Main agent orchestrator using LangChain with GPT-3.5-turbo
"""

import os
from typing import Dict, Any, List
from dotenv import load_dotenv

from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain_openai import Chat<PERSON><PERSON>A<PERSON>
from langchain_core.prompts import Chat<PERSON>rom<PERSON>Template, MessagesPlaceholder
from langchain_core.messages import HumanMessage
from langchain_core.tools import BaseTool

# Import our custom tools
from .tools.intent_extractor import IntentExtractionTool
from .tools.web_scraper import WebScrapingTool
from .tools.product_processor import ProductProcessingTool
from .tools.conversational_responder import ConversationalResponderTool
from .tools.whatsapp_sender import WhatsAppSenderTool

load_dotenv()

class DealerAIAgent:
    """
    Main Dealer AI Agent class that orchestrates the entire workflow
    using Lang<PERSON>hain's agent framework with GPT-3.5-turbo
    """
    
    def __init__(self):
        self.openrouter_api_key = os.getenv("OPENROUTER_API_KEY")
        self.debug = os.getenv("DEBUG", "false").lower() == "true"
        
        # Initialize the LLM with OpenRouter
        self.llm = ChatOpenAI(
            model="openai/gpt-3.5-turbo",
            openai_api_key=self.openrouter_api_key,
            openai_api_base="https://openrouter.ai/api/v1",
            temperature=0.1,
            max_tokens=2000
        )
        
        # Initialize tools
        self.tools = self._initialize_tools()
        
        # Create the agent
        self.agent = self._create_agent()
        
        # Create agent executor with retry logic
        self.agent_executor = AgentExecutor(
            agent=self.agent,
            tools=self.tools,
            verbose=self.debug,
            max_iterations=10,
            max_execution_time=300,  # 5 minutes timeout
            return_intermediate_steps=True
        )
    
    def _initialize_tools(self) -> List[BaseTool]:
        """Initialize all the tools the agent can use"""
        return [
            IntentExtractionTool(),
            WebScrapingTool(),
            ProductProcessingTool(),
            ConversationalResponderTool(),
            WhatsAppSenderTool()
        ]
    
    def _create_agent(self):
        """Create the LangChain agent with proper prompting"""
        
        system_prompt = """You are Dealer AI, an intelligent WhatsApp-based shopping assistant that helps users find the best product deals across e-commerce platforms.

Your capabilities:
1. Extract structured intent from user messages in any language (Swahili, English, etc.)
2. Decide whether a message is about product deals or general conversation
3. For product requests: scrape jiji.co.tz to find matching products
4. For general queries: respond conversationally while staying in character

WORKFLOW FOR PRODUCT REQUESTS:
1. Use intent_extractor to extract: product_name, max_price, condition, site_preference
2. Use web_scraper to search jiji.co.tz with the extracted information
3. Use product_processor to format and select the best products
4. Use whatsapp_sender to send formatted results back to user

WORKFLOW FOR GENERAL QUERIES:
1. Use conversational_responder to generate appropriate response in user's language
2. Use whatsapp_sender to send the response

IMPORTANT RULES:
- Always stay in character as Dealer AI
- Respond in the same language the user used
- For product requests, always try to find deals even if the request is vague
- If no products found, politely suggest alternatives
- Never go off-topic from your core purpose (finding deals)
- Be helpful, friendly, and concise in responses

Available tools: {tool_names}
Tool descriptions: {tools}"""

        prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            MessagesPlaceholder("chat_history", optional=True),
            ("human", "{input}"),
            MessagesPlaceholder("agent_scratchpad")
        ])
        
        return create_openai_tools_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=prompt
        )
    
    async def process_message(self, user_message: str, user_phone: str = None) -> Dict[str, Any]:
        """
        Main entry point for processing user messages
        
        Args:
            user_message: The message from the user
            user_phone: Optional phone number for personalized responses
            
        Returns:
            Dict containing the response and metadata
        """
        try:
            if self.debug:
                print(f"Processing message: {user_message}")
            
            # Execute the agent
            result = await self.agent_executor.ainvoke({
                "input": user_message,
                "user_phone": user_phone or "unknown"
            })
            
            return {
                "success": True,
                "response": result.get("output", ""),
                "intermediate_steps": result.get("intermediate_steps", []),
                "user_message": user_message
            }
            
        except Exception as e:
            error_msg = f"Error processing message: {str(e)}"
            if self.debug:
                print(error_msg)
            
            # Fallback response
            fallback_response = await self._generate_fallback_response(user_message)
            
            return {
                "success": False,
                "error": error_msg,
                "response": fallback_response,
                "user_message": user_message
            }
    
    async def _generate_fallback_response(self, user_message: str) -> str:
        """Generate a fallback response when the main agent fails"""
        try:
            fallback_prompt = f"""
            You are Dealer AI. The user sent: "{user_message}"
            
            Something went wrong with the main system. Provide a brief, helpful response 
            acknowledging the issue and asking them to try again. Respond in the same 
            language as the user's message.
            """
            
            response = await self.llm.ainvoke([HumanMessage(content=fallback_prompt)])
            return response.content
            
        except Exception:
            return "Sorry, I'm experiencing technical difficulties. Please try again in a moment. / Samahani, nina matatizo ya kiufundi. Tafadhali jaribu tena baada ya muda."

# Global agent instance
_agent_instance = None

def get_agent() -> DealerAIAgent:
    """Get or create the global agent instance"""
    global _agent_instance
    if _agent_instance is None:
        _agent_instance = DealerAIAgent()
    return _agent_instance

async def process_whatsapp_message(message: str, phone: str = None) -> Dict[str, Any]:
    """Convenience function for processing WhatsApp messages"""
    agent = get_agent()
    return await agent.process_message(message, phone)
