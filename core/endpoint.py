from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import PlainTextResponse
from .agent import process_whatsapp_message

app = FastAPI(title="Dealer AI", description="Autonomous WhatsApp Shopping Assistant")

@app.post("/whatsapp")
async def whatsapp(request: Request):
    """
    Main WhatsApp webhook endpoint
    Processes incoming messages using the LangChain agent
    """
    try:
        # Get form data from Twilio
        form = await request.form()
        body = form.get('Body', '').strip()
        from_number = form.get('From', '')

        print(f"Received message: {body} from {from_number}")

        if not body:
            return PlainTextResponse("OK")

        # Extract phone number for personalization
        phone = from_number.replace('whatsapp:', '') if from_number else None

        # Process message with the LangChain agent
        result = await process_whatsapp_message(body, phone)

        if result.get("success"):
            print(f"Agent processed successfully: {result.get('response', 'No response')}")
        else:
            print(f"Agent processing failed: {result.get('error', 'Unknown error')}")

        return PlainTextResponse("OK")

    except Exception as e:
        print(f"Webhook error: {e}")
        return PlainTextResponse("OK")  # Always return OK to Twilio

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "Dealer AI is running", "status": "healthy"}

@app.get("/health")
async def health_check():
    """Detailed health check"""
    try:
        # Test agent initialization
        from .agent import get_agent
        agent = get_agent()

        return {
            "status": "healthy",
            "agent": "initialized",
            "tools": len(agent.tools),
            "message": "All systems operational"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "Agent initialization failed"
        }

@app.post("/test")
async def test_message(request: Request):
    """Test endpoint for debugging messages"""
    try:
        data = await request.json()
        message = data.get("message", "")
        phone = data.get("phone", "+255000000000")

        if not message:
            raise HTTPException(status_code=400, detail="Message is required")

        result = await process_whatsapp_message(message, phone)
        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))