import json
from fastapi import FastAPI, Request
from fastapi.responses import PlainTextResponse
from .send_message import send_message
from .model_reply import dealer_ai_extract, dealer_ai_reply
from .scrap import deal_scraper

app = FastAPI()

@app.post("/whatsapp")
async def whatsapp(request: Request):
    form = await request.form()
    body = form.get('Body', '').strip()
    print(f"Got message: {body}")
    
    # 1. Extract structured info from the message
    extracted = dealer_ai_extract(body)
    print(f"Extracted: {extracted}")

    # 2. Check if this is a product request or not
    if extracted["category"] == "N/A":
        reply_text = dealer_ai_reply(body)
        send_message(reply_text)
    else:
        try:
            # deals = deal_scraper(
            #     category=extracted["category"],
            #     brand=extracted["brand"],
            #     price=extracted.get("price"),
            #     condition=extracted["condition"]
            # )

            try:
                result = await deal_scraper(prompt=extracted)
            except Exception as e:
                print(f"Scrapped result error: {e}")
                return
            
            print(f"The result: {result}\nInfo: {result['info']}")
            if result['info'] == 'Done':
                with open("store/products.json", "r", encoding="utf-8") as f:
                    deals = json.load(f)
                

                if deals != []:
                    product_emoji = "💻"
                    desc_emoji = "📝"
                    link_emoji = "🔗"
                    chat_emoji = "💬"
                    vendor_number = "+255763414078"
                    for deal in deals[:1]:
                        # message = (
                        #     f"🌟 *Deal Alert* 🌟\n"
                        #     f"{product_emoji} *Price:*\n {deal['price'].split(' ')[2]}\n"
                        #     f"{desc_emoji} *Description:*\n_{deal['description']}_\n"
                        #     f"{link_emoji} *View Deal:* [Click here]({deal['link']})\n"
                        #     f"{chat_emoji} *Chat with Vendor:*\n[Tap to chat](https://wa.me/{vendor_number.replace('+', '')})\n"
                        # )

                        message = (
                            f"{deal['price']}\n"
                            f"{deal['description']}\n"
                            # f"{link_emoji} *View Deal:* ({deal['link']})\n"
                            f"Chat with Vendor:*\n(https://wa.me/{vendor_number.replace('+', '')})\n"
                        )

                        reply_text = dealer_ai_reply(f"""
                                Reword the following product details into a friendly WhatsApp message. 
                                Keep it short, I mean really short (don't be too wordy), easy to understand, and attractive. 
                                These must be included the price, chat with vendor link and description (summarize if its too long). 
                                Example of reply you should give your structure in:
                                                     
                                🌟Deal Alert🌟
                                📱Infinix Smart 8 -> 6.6", 128GB + 4GB RAM, 5000mAh, 4G (Dual SIM) - Timber Black (1YR WRTY)
                                💰Only for: TZS 295,000
                                💬Chat with the vendor: 
                                  https://wa.me/+255763414078
                                
                                Use that as the example of the reply, your restructuring of the message should match the above, MUST:
                                {message}
                            """)
                        send_message(reply_text, image_url=deal["image"])
                    return PlainTextResponse("Done sending deals.")
                else:
                    reply_text = dealer_ai_reply("Give a friendly polite reply informing the user that no matching deals found for their request.")
                    send_message(reply_text)

            else:
                reply_text = dealer_ai_reply("Give a friendly polite reply informing the user that unknown error occured. You may give some possible options and tell them to try again.")
                send_message(reply_text)
        except Exception as e:
            print(f"Scraper error: {e}")
            send_message("Something went wrong while looking for your product. Please try again.")
