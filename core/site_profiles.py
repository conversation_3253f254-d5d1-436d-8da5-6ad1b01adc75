"""
Site Profiles for Dealer AI
Modular configuration for different e-commerce sites
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

class SiteType(Enum):
    JIJI = "jiji"
    JUMIA = "jumia"
    KIKUU = "kikuu"

@dataclass
class SiteProfile:
    """Configuration profile for an e-commerce site"""
    name: str
    base_url: str
    search_url_pattern: str
    selectors: Dict[str, str]
    filters: Dict[str, Dict[str, Any]]
    pagination: Dict[str, Any]
    product_card_selector: str
    max_products_per_page: int
    requires_javascript: bool = True
    wait_after_search: int = 3000  # milliseconds
    scroll_behavior: Dict[str, Any] = None

# Jiji.co.tz Profile
JIJI_PROFILE = SiteProfile(
    name="Jiji Tanzania",
    base_url="https://jiji.co.tz",
    search_url_pattern="https://jiji.co.tz/search?query={query}",
    selectors={
        "search_bar": "input[name='query']",
        "search_button": "button[type='submit']",
        "product_cards": "div[data-testid='advert-list-item']",
        "product_title": "h3[data-testid='advert-title']",
        "product_price": "p[data-testid='advert-price']",
        "product_image": "img[data-testid='advert-image']",
        "product_link": "a[data-testid='advert-link']",
        "product_location": "p[data-testid='advert-location']",
        "load_more_button": "button[data-testid='load-more']",
        "no_results": "div[data-testid='no-results']",
        # Filter selectors
        "price_filter_min": "input[name='price_from']",
        "price_filter_max": "input[name='price_to']",
        "condition_filter": "select[name='condition']",
        "category_filter": "select[name='category']",
        "apply_filters_button": "button[data-testid='apply-filters']"
    },
    filters={
        "price": {
            "min_selector": "input[name='price_from']",
            "max_selector": "input[name='price_to']",
            "apply_method": "input_and_submit"
        },
        "condition": {
            "selector": "select[name='condition']",
            "options": {
                "new": "new",
                "used": "used",
                "refurbished": "refurbished"
            },
            "apply_method": "select_and_submit"
        },
        "category": {
            "selector": "select[name='category']",
            "options": {
                "mobile-phones": "mobile-phones-tablets",
                "laptops": "computers-laptops",
                "electronics": "electronics"
            },
            "apply_method": "select_and_submit"
        }
    },
    pagination={
        "type": "infinite_scroll",
        "load_more_selector": "button[data-testid='load-more']",
        "auto_scroll": True,
        "scroll_pause": 2000
    },
    product_card_selector="div[data-testid='advert-list-item']",
    max_products_per_page=20,
    requires_javascript=True,
    wait_after_search=3000,
    scroll_behavior={
        "enabled": True,
        "max_scrolls": 3,
        "scroll_pause": 2000,
        "scroll_distance": 1000
    }
)

# Alternative selectors for Jiji (in case the main ones don't work)
JIJI_FALLBACK_SELECTORS = {
    "search_bar": ["input[name='query']", "input[placeholder*='Search']", "#search-input"],
    "search_button": ["button[type='submit']", "button.search-btn", ".search-button"],
    "product_cards": [
        "div[data-testid='advert-list-item']",
        ".advert-list-item",
        ".product-item",
        ".listing-item"
    ],
    "product_title": [
        "h3[data-testid='advert-title']",
        ".advert-title",
        ".product-title",
        "h3 a",
        ".listing-title"
    ],
    "product_price": [
        "p[data-testid='advert-price']",
        ".advert-price",
        ".price",
        ".product-price"
    ],
    "product_image": [
        "img[data-testid='advert-image']",
        ".advert-image img",
        ".product-image img",
        ".listing-image img"
    ],
    "product_link": [
        "a[data-testid='advert-link']",
        ".advert-link",
        ".product-link",
        "h3 a"
    ]
}

class SiteProfileManager:
    """Manages site profiles and provides site-specific configurations"""
    
    def __init__(self):
        self.profiles = {
            SiteType.JIJI: JIJI_PROFILE
        }
        self.fallback_selectors = {
            SiteType.JIJI: JIJI_FALLBACK_SELECTORS
        }
    
    def get_profile(self, site_type: SiteType) -> SiteProfile:
        """Get the profile for a specific site"""
        return self.profiles.get(site_type)
    
    def get_fallback_selectors(self, site_type: SiteType) -> Dict[str, List[str]]:
        """Get fallback selectors for a site"""
        return self.fallback_selectors.get(site_type, {})
    
    def get_search_url(self, site_type: SiteType, query: str) -> str:
        """Generate search URL for a site with the given query"""
        profile = self.get_profile(site_type)
        if not profile:
            raise ValueError(f"No profile found for site: {site_type}")
        
        return profile.search_url_pattern.format(query=query)
    
    def get_category_mapping(self, site_type: SiteType, category: str) -> Optional[str]:
        """Get site-specific category mapping"""
        profile = self.get_profile(site_type)
        if not profile:
            return None
        
        category_options = profile.filters.get("category", {}).get("options", {})
        return category_options.get(category)
    
    def supports_site(self, site_name: str) -> bool:
        """Check if a site is supported"""
        site_name_lower = site_name.lower()
        for site_type in self.profiles:
            if site_type.value in site_name_lower:
                return True
        return False
    
    def get_site_type_from_name(self, site_name: str) -> Optional[SiteType]:
        """Get SiteType enum from site name string"""
        site_name_lower = site_name.lower()
        if "jiji" in site_name_lower:
            return SiteType.JIJI
        elif "jumia" in site_name_lower:
            return SiteType.JUMIA
        elif "kikuu" in site_name_lower:
            return SiteType.KIKUU
        return None

# Global instance
site_manager = SiteProfileManager()

def get_site_profile(site_name: str = "jiji") -> Optional[SiteProfile]:
    """Convenience function to get a site profile"""
    site_type = site_manager.get_site_type_from_name(site_name)
    if site_type:
        return site_manager.get_profile(site_type)
    return None
